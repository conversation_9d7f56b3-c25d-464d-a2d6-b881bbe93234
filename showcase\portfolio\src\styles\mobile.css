/* Mobile Optimization Styles */

/* Ensure proper spacing from navigation */
body {
  padding-top: 0;
  padding-bottom: 0;
}

/* Bottom navigation spacing */
@media (max-width: 768px) {
  body {
    padding-bottom: 80px;
  }

  /* Last section needs extra padding for bottom nav */
  section:last-of-type {
    padding-bottom: 100px !important;
  }
}

/* Navigation fixes */
.nav-mobile {
  background: rgba(0, 0, 0, 0.9) !important;
  backdrop-filter: blur(10px);
}

/* Hero section mobile fixes */
@media (max-width: 768px) {
  /* Prevent overlapping with navigation */
  section:first-of-type {
    padding-top: 5rem !important; /* Reduced gap from header */
    min-height: 75vh !important; /* Reduced height to create better spacing */
    /* Move hero content lower on mobile */
    align-items: flex-start !important;
  }

  /* Hero Section Mobile */
  .hero-mobile {
    padding: 1rem;
    min-height: calc(100vh - 4rem);
  }

  /* Move hero content to middle-lower position on mobile */
  section:first-of-type .container {
    margin-top: 8vh !important; /* Reduced gap from header */
  }

  /* Projects section mobile spacing fix */
  #projects {
    padding-top: 2rem !important; /* Reduced top padding */
    padding-bottom: 3rem !important; /* Reduced bottom padding */
  }

  /* Reduce gap between hero and projects */
  section:first-of-type + section {
    margin-top: -2rem !important; /* Pull projects section up */
  }

  /* About section two-container layout mobile optimization */
  .about-mobile .grid {
    grid-template-columns: 1fr !important; /* Stack containers on mobile */
    gap: 1.5rem !important;
  }

  .about-mobile img[alt="Dhruba Kumar Agarwalla"] {
    width: 12rem !important; /* 192px - larger since it has its own container */
    height: 15rem !important; /* 240px - portrait aspect ratio */
    max-width: 100% !important;
  }

  /* Responsive text sizing */
  h1 {
    font-size: 2rem !important;
    line-height: 1.1 !important;
    margin-bottom: 1rem !important;
  }

  h2 {
    font-size: 1.25rem !important;
    margin-bottom: 1rem !important;
  }

  h3 {
    font-size: 1.125rem !important;
    margin-bottom: 0.75rem !important;
  }

  /* Stats grid mobile */
  .hero-stats {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 0.5rem !important;
  }

  /* Button improvements */
  .hero-buttons {
    flex-direction: column !important;
    gap: 0.75rem !important;
    width: 100% !important;
  }

  .hero-button {
    width: 100% !important;
    justify-content: center !important;
    padding: 0.75rem 1.5rem !important;
  }

  /* Navigation Mobile */
  .nav-mobile {
    padding: 1rem;
  }

  .nav-logo {
    font-size: 1.25rem;
  }

  /* Fix Portfolio text centering in mobile navigation */
  .mobile-portfolio-text {
    position: absolute !important;
    left: 50% !important;
    top: 50% !important;
    transform: translate(-50%, -50%) !important;
    z-index: 10;
    width: auto !important;
    text-align: center;
    white-space: nowrap;
  }

  /* Ensure parent container allows proper centering */
  nav .flex.items-center.justify-between {
    position: relative;
  }

  /* Additional mobile navigation fixes */
  @media (max-width: 640px) {
    .mobile-portfolio-text {
      font-size: 1.125rem !important; /* Slightly smaller on very small screens */
    }
  }

  /* Prevent layout shifts in mobile navigation */
  nav {
    min-height: 4rem; /* Ensure consistent height */
  }

  /* Ensure left and right elements don't interfere with center positioning */
  nav .flex.items-center.justify-between > div:first-child {
    flex-shrink: 0;
    max-width: 35%; /* Limit width to prevent overlap */
  }

  nav .flex.items-center.justify-between > div:last-child {
    flex-shrink: 0;
    max-width: 35%; /* Limit width to prevent overlap */
  }

  /* Projects Section Mobile */
  .projects-mobile {
    padding: 2rem 1rem; /* Reduced from 3rem */
  }

  /* Better mobile section spacing */
  section {
    margin-bottom: 1rem !important; /* Reduce gaps between sections */
  }

  /* Specific spacing for projects section title */
  #projects h2 {
    margin-bottom: 1.5rem !important; /* Reduced from default */
    font-size: 2rem !important; /* Mobile-optimized title size */
    line-height: 1.1 !important;
  }

  #projects .text-center.mb-16 {
    margin-bottom: 2rem !important; /* Reduced from mb-16 */
  }

  /* Projects section description mobile optimization */
  #projects p {
    font-size: 0.875rem !important; /* Readable mobile description */
    line-height: 1.4 !important;
    margin-bottom: 1.5rem !important;
    padding: 0 1rem !important; /* Add side padding */
  }

  .projects-grid {
    grid-template-columns: 1fr !important;
    gap: 1.25rem;
    max-width: 100% !important;
    padding: 0 0.5rem;
  }

  /* Projects section mobile grid optimization */
  #projects .grid {
    grid-template-columns: 1fr !important;
    gap: 1.25rem !important;
  }

  .project-card {
    padding: 1rem !important; /* Balanced compact padding */
    margin: 0;
  }

  /* Optimize project card content for mobile readability */
  .project-card .flex.justify-between.items-start {
    margin-bottom: 0.75rem !important; /* Balanced spacing */
  }

  .project-card h3 {
    font-size: 1.125rem !important; /* Readable title size */
    line-height: 1.2 !important;
    font-weight: 700 !important;
  }

  .project-card p {
    font-size: 0.875rem !important; /* Readable description text */
    line-height: 1.4 !important;
    margin-bottom: 0.75rem !important; /* Balanced spacing */
  }

  /* Technology tags optimized for mobile */
  .project-card .flex.flex-wrap {
    gap: 0.375rem !important; /* Readable gap between tech tags */
    margin-bottom: 0.75rem !important; /* Balanced spacing */
  }

  .project-card .flex.flex-wrap span {
    padding: 0.25rem 0.5rem !important; /* Touch-friendly padding */
    font-size: 0.75rem !important; /* Readable tech tag text */
    white-space: nowrap !important; /* Prevent text wrapping */
  }

  /* Buttons optimized for mobile touch */
  .project-card .flex.gap-3 {
    gap: 0.75rem !important; /* Touch-friendly button gap */
  }

  .project-card .flex.gap-3 button {
    font-size: 0.875rem !important; /* Readable button text */
    padding: 0.5rem !important; /* Touch-friendly padding */
    min-height: 44px !important; /* Touch target size */
  }

  .project-card .flex.gap-3 button svg {
    width: 1rem !important; /* Readable icons */
    height: 1rem !important;
  }

  /* Optimize arrow icon for mobile */
  .project-card .p-2.rounded-full {
    padding: 0.5rem !important; /* Touch-friendly arrow container */
    min-height: 44px !important; /* Touch target size */
    min-width: 44px !important;
  }

  .project-card .p-2.rounded-full svg {
    width: 1rem !important; /* Readable arrow icon */
    height: 1rem !important;
  }

  /* Project image optimization for mobile */
  .project-card img {
    height: 8rem !important; /* Consistent mobile image height */
    object-fit: cover !important;
  }

  .project-title {
    font-size: 1.5rem !important;
    line-height: 1.2;
  }

  .project-description {
    font-size: 0.95rem;
    line-height: 1.5;
  }

  .project-tech-tags {
    gap: 0.5rem;
  }

  .project-tech-tag {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
  }

  .project-buttons {
    gap: 0.75rem;
    flex-wrap: wrap;
  }

  /* About Section Mobile */
  .about-mobile {
    padding: 3rem 1rem;
  }

  .about-title {
    font-size: 2.5rem !important;
    margin-bottom: 1.5rem;
  }

  .about-description {
    font-size: 1rem;
    max-width: 100% !important;
  }

  .about-story-card {
    padding: 1.5rem !important;
    margin-bottom: 2rem;
  }

  .achievements-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 1rem;
  }

  .achievement-card {
    padding: 1rem !important;
  }

  .methodology-grid {
    grid-template-columns: 1fr !important;
    gap: 1rem;
  }

  .methodology-card {
    padding: 1rem !important;
  }

  /* Tech Stack Mobile */
  .tech-mobile {
    padding: 3rem 1rem;
  }

  .tech-title {
    font-size: 2.5rem !important;
    margin-bottom: 1.5rem;
  }

  .tech-grid {
    grid-template-columns: 1fr !important;
    gap: 1.5rem;
  }

  .tech-category-card {
    padding: 1.5rem !important;
  }

  .tech-category-title {
    font-size: 1.25rem !important;
  }

  .tech-item {
    margin-bottom: 1rem;
  }

  .tech-item-name {
    font-size: 0.95rem;
  }

  .tech-item-description {
    font-size: 0.8rem;
  }

  /* Contact Section Mobile */
  .contact-mobile {
    padding: 3rem 1rem;
  }

  .contact-title {
    font-size: 2.5rem !important;
    margin-bottom: 1.5rem;
  }

  .contact-grid {
    grid-template-columns: 1fr !important;
    gap: 2rem;
  }

  .contact-info-card {
    padding: 1rem !important;
  }

  .contact-social-card {
    padding: 1rem !important;
  }

  .collaboration-card {
    padding: 1.25rem !important;
  }

  /* Modal Mobile */
  .modal-mobile {
    margin: 1rem;
    max-height: 90vh;
    width: calc(100vw - 2rem) !important;
    max-width: none !important;
  }

  .modal-content {
    padding: 1.5rem !important;
  }

  .modal-title {
    font-size: 2rem !important;
    line-height: 1.2;
  }

  .modal-image {
    height: 200px !important;
  }

  .modal-tech-grid {
    gap: 0.5rem;
  }

  .modal-tech-tag {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
  }

  .modal-highlights-grid {
    grid-template-columns: 1fr !important;
    gap: 0.75rem;
  }

  .modal-buttons {
    flex-direction: column;
    gap: 0.75rem;
  }

  .modal-button {
    width: 100%;
    justify-content: center;
  }

  /* Loading Screen Mobile */
  .loading-mobile {
    padding: 2rem 1rem;
  }

  .loading-title {
    font-size: 2.5rem !important;
  }

  .loading-subtitle {
    font-size: 1rem !important;
  }

  .loading-stats {
    grid-template-columns: repeat(3, 1fr);
    gap: 0.75rem;
  }

  .loading-stat-card {
    padding: 0.75rem !important;
  }

  /* Scroll to Top Mobile */
  .scroll-to-top-mobile {
    bottom: 1rem !important;
    left: 1rem !important;
    padding: 0.75rem !important;
  }

  /* Glass Card Mobile */
  .glass-card-mobile {
    backdrop-filter: blur(8px) !important;
    border-radius: 1rem !important;
  }

  /* Text Sizes Mobile */
  .text-mobile-xl {
    font-size: 1.25rem !important;
  }

  .text-mobile-lg {
    font-size: 1.125rem !important;
  }

  .text-mobile-base {
    font-size: 1rem !important;
  }

  .text-mobile-sm {
    font-size: 0.875rem !important;
  }

  .text-mobile-xs {
    font-size: 0.75rem !important;
  }

  /* Spacing Mobile */
  .spacing-mobile-tight {
    padding: 1rem !important;
  }

  .spacing-mobile-normal {
    padding: 1.5rem !important;
  }

  .spacing-mobile-loose {
    padding: 2rem !important;
  }

  /* Animation Performance Mobile */
  .reduce-motion {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Extra small devices (phones in portrait) */
@media (max-width: 480px) {
  /* Further reduce hero section height on very small screens */
  section:first-of-type {
    padding-top: 4.5rem !important; /* Reduced gap from header */
    min-height: 70vh !important; /* Smaller height */
    align-items: flex-start !important;
  }

  /* Adjust container positioning for smaller screens */
  section:first-of-type .container {
    margin-top: 6vh !important; /* Reduced margin for smaller screens */
  }

  /* Tighter spacing for projects section */
  #projects {
    padding-top: 1.5rem !important;
    padding-bottom: 2rem !important;
  }

  /* Pull projects section even closer */
  section:first-of-type + section {
    margin-top: -3rem !important;
  }

  .hero-title {
    font-size: 2rem !important;
  }

  .hero-subtitle {
    font-size: 1.125rem !important;
  }

  .hero-stats {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  /* Extra small profile photo in separate container */
  img[alt="Dhruba Kumar Agarwalla"] {
    width: 10rem !important; /* 160px */
    height: 12rem !important; /* 192px */
    max-width: 100% !important;
  }

  .project-card {
    padding: 0.875rem !important; /* Compact but readable card padding */
  }

  /* Compact project card content for very small screens */
  .project-card .flex.justify-between.items-start {
    margin-bottom: 0.5rem !important; /* Minimal but readable spacing */
  }

  .project-card h3 {
    font-size: 1rem !important; /* Readable title on small screens */
    line-height: 1.1 !important;
    font-weight: 700 !important;
  }

  .project-card p {
    font-size: 0.8125rem !important; /* Readable description */
    line-height: 1.3 !important;
    margin-bottom: 0.5rem !important; /* Balanced spacing */
  }

  .project-card .flex.flex-wrap {
    gap: 0.25rem !important; /* Minimal but readable gap */
    margin-bottom: 0.5rem !important; /* Balanced spacing */
  }

  .project-card .flex.flex-wrap span {
    padding: 0.1875rem 0.375rem !important; /* Compact but touch-friendly */
    font-size: 0.6875rem !important; /* Small but readable tech tags */
  }

  .project-card .flex.gap-3 {
    gap: 0.5rem !important; /* Touch-friendly button gap */
  }

  .project-card .flex.gap-3 button {
    font-size: 0.8125rem !important; /* Readable button text */
    padding: 0.375rem !important; /* Touch-friendly padding */
    min-height: 40px !important; /* Adequate touch target */
  }

  .project-card .flex.gap-3 button svg {
    width: 0.875rem !important; /* Readable icons */
    height: 0.875rem !important;
  }

  /* Compact but usable arrow icon */
  .project-card .p-2.rounded-full {
    padding: 0.375rem !important; /* Touch-friendly arrow container */
    min-height: 40px !important; /* Adequate touch target */
    min-width: 40px !important;
  }

  .project-card .p-2.rounded-full svg {
    width: 0.875rem !important; /* Readable arrow icon */
    height: 0.875rem !important;
  }

  /* Project image optimization for small screens */
  .project-card img {
    height: 7rem !important; /* Slightly smaller for very small screens */
  }

  .about-title,
  .tech-title,
  .contact-title {
    font-size: 2rem !important;
  }

  .modal-mobile {
    margin: 0.5rem;
    width: calc(100vw - 1rem) !important;
  }

  .modal-content {
    padding: 1rem !important;
  }
}

/* Landscape orientation adjustments */
@media (max-width: 768px) and (orientation: landscape) {
  .hero-mobile {
    min-height: auto;
    padding: 1.5rem 1rem;
  }

  /* Adjust hero positioning for landscape */
  section:first-of-type {
    align-items: center !important; /* Center in landscape for better fit */
  }

  section:first-of-type .container {
    margin-top: 3vh !important; /* Further reduced margin for landscape */
  }

  .hero-title {
    font-size: 2rem !important;
  }

  .hero-subtitle {
    font-size: 1rem !important;
  }

  .hero-stats {
    grid-template-columns: repeat(4, 1fr);
    gap: 0.5rem;
  }
}

/* Touch-friendly improvements */
@media (hover: none) and (pointer: coarse) {
  .touch-friendly {
    min-height: 44px;
    min-width: 44px;
  }

  .project-card {
    transform: none !important;
  }

  .project-card:active {
    transform: scale(0.98) !important;
  }

  .nav-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
}

/* Chatbot positioning fixes */
@media (max-width: 768px) {
  /* Ensure chatbot doesn't overlap with bottom navigation */
  /* Target chatbot button specifically */
  .fixed.z-50.w-14.h-14 {
    bottom: 5.5rem !important; /* 88px - above bottom navigation */
  }

  /* Target chatbot window specifically */
  .fixed.z-50.w-96 {
    bottom: 5.5rem !important; /* 88px - above bottom navigation */
  }

  /* Adjust for very small screens */
  @media (max-width: 480px) {
    .fixed.z-50.w-14.h-14 {
      bottom: 5rem !important; /* 80px - slightly less spacing on very small screens */
      right: 1rem !important; /* 16px - closer to edge on small screens */
    }

    .fixed.z-50.w-96 {
      bottom: 5rem !important; /* 80px - match button position */
      right: 1rem !important; /* 16px - closer to edge on small screens */
      width: calc(100vw - 2rem) !important; /* Full width minus margins */
    }
  }
}
