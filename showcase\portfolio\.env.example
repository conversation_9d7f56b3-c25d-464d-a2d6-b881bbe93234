# AI Chatbot Configuration
VITE_GROQ_API_KEY=your_groq_api_key_here
VITE_GITHUB_TOKEN=your_github_token_here

# GitHub Repository Configuration
VITE_GITHUB_USERNAME=your_github_username
VITE_GITHUB_REPOS=repo1,repo2,repo3

# Chatbot Settings
VITE_CHATBOT_NAME=Your AI Assistant
VITE_CHATBOT_PERSONALITY=professional_technical
VITE_CHATBOT_MODEL=llama3-8b-8192
VITE_MAX_CONTEXT_LENGTH=8000
VITE_RESPONSE_TEMPERATURE=0.7

# Portfolio Owner Information
VITE_OWNER_NAME=Your Name
VITE_OWNER_TITLE=Your Title
VITE_OWNER_EMAIL=<EMAIL>
VITE_OWNER_PHONE=+1234567890
VITE_OWNER_LOCATION=Your Location

# Project Specific Information
VITE_EVENT_MANAGER_LINES=75000
VITE_GITIQ_LINES=40000
VITE_PORTFOLIO_LINES=15000

# API Rate Limiting
VITE_RATE_LIMIT_REQUESTS=50
VITE_RATE_LIMIT_WINDOW=3600000

# Development Settings
VITE_DEBUG_MODE=false
VITE_LOG_CONVERSATIONS=false
