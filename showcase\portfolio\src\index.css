
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 220 13% 5%;
    --foreground: 210 40% 98%;

    --card: 220 13% 8%;
    --card-foreground: 210 40% 98%;

    --popover: 220 13% 8%;
    --popover-foreground: 210 40% 98%;

    --primary: 195 100% 50%;
    --primary-foreground: 220 13% 5%;

    --secondary: 262 52% 47%;
    --secondary-foreground: 210 40% 98%;

    --muted: 220 13% 12%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 262 52% 47%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 220 13% 15%;
    --input: 220 13% 15%;
    --ring: 195 100% 50%;

    --radius: 0.5rem;

    --sidebar-background: 220 13% 5%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 195 100% 50%;
    --sidebar-primary-foreground: 220 13% 5%;
    --sidebar-accent: 220 13% 12%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 220 13% 15%;
    --sidebar-ring: 195 100% 50%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html {
    /* Prevent white flash during fast scrolling */
    background-color: #0a0a0a !important;
    background-image: radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.08) 0%, transparent 50%),
                      radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.08) 0%, transparent 50%);
    background-attachment: fixed;
    scroll-behavior: smooth;
    overflow-x: hidden;
  }

  body {
    @apply bg-background text-foreground font-rajdhani;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
    background-attachment: fixed;
    min-height: 100vh;
    margin: 0;
    padding: 0;
    /* Ensure no white background shows */
    background-color: #0a0a0a !important;
    /* Prevent layout shifts */
    overflow-x: hidden;
  }

  /* Scrollbar styling */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
  }

  ::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #00d4ff, #8b5cf6);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, #00a8cc, #7c3aed);
  }
}

@layer components {
  .glass-card {
    @apply backdrop-blur-md bg-white/5 border border-white/10 rounded-2xl;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3), 
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  .cyber-grid {
    background-image: 
      linear-gradient(rgba(0, 212, 255, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
  }

  .text-cyber {
    background: linear-gradient(135deg, #00d4ff, #8b5cf6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .neon-glow {
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
  }

  .particles {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
  }

  /* Enhanced Navigation Styles */
  .nav-brand-logo {
    background: linear-gradient(135deg, #00d4ff, #8b5cf6);
    box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
  }

  .nav-brand-logo:hover {
    box-shadow: 0 6px 20px rgba(0, 212, 255, 0.4);
    transform: translateY(-1px);
  }

  .nav-link-active {
    position: relative;
    background: rgba(0, 212, 255, 0.1);
    border: 1px solid rgba(0, 212, 255, 0.3);
  }

  .nav-link-active::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(139, 92, 246, 0.1));
    border-radius: inherit;
    z-index: -1;
  }

  /* Anti-flash optimizations */
  #root {
    background-color: #0a0a0a !important;
    min-height: 100vh;
    width: 100%;
    position: relative;
  }

  /* Prevent white flash on route changes */
  .page-container {
    background-color: #0a0a0a;
    min-height: 100vh;
    width: 100%;
  }

  /* Optimize rendering performance */
  * {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }

  /* Prevent flash of unstyled content */
  .loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
    z-index: 9999;
    pointer-events: none;
  }

  /* Custom scrollbar for webkit browsers */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
  }

  ::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #00d4ff, #8b5cf6);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #00b8e6, #7c3aed);
  }
}
