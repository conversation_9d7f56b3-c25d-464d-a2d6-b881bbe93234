# 🚀 AI-Orchestrated Portfolio

<div align="center">

![Portfolio Banner](https://img.shields.io/badge/Portfolio-AI--Orchestrated-blue?style=for-the-badge&logo=react)
![Lines of Code](https://img.shields.io/badge/Lines%20of%20Code-155K+-green?style=for-the-badge)
![Build Status](https://img.shields.io/badge/Build-Passing-brightgreen?style=for-the-badge)
![License](https://img.shields.io/badge/License-MIT-yellow?style=for-the-badge)

**🌟 Website**: [portfolio-dhruba.vercel.app](https://portfolio-dhruba.vercel.app/)

</div>

## 👨‍💻 About

**Dhr<PERSON> Kumar <PERSON>walla** - AI-Orchestrated Full-Stack Developer
*2nd Year Civil Engineering Student at NIT Silchar*

A revolutionary portfolio showcasing the future of AI-driven development through **155,000+ lines** of production-ready code built with strategic AI collaboration and **$0 budget**.

### ✨ Portfolio Highlights

- **🤖 AI-Orchestrated Development**: Pioneering human-AI collaboration methodology
- **💰 Zero Budget, Maximum Impact**: 155,000+ lines of code with $0 investment
- **🏆 Award-Winning Developer**: 2nd place CSS Hacks Hackathon winner
- **⚡ Ultra-Fast Development**: 75K lines in 3-4 weeks, 40K lines in <1 week
- **🎯 Real-World Impact**: Projects deployed and actively used at NIT Silchar
- **🤖 Intelligent AI Assistant**: Built-in chatbot with project knowledge

## 🛠️ Tech Stack

<div align="center">

| Category | Technologies |
|----------|-------------|
| **Frontend** | React 18, TypeScript, Tailwind CSS, Framer Motion |
| **Build Tools** | Vite, PostCSS, Autoprefixer |
| **AI Integration** | Groq AI (llama3-8b-8192), GitHub API |
| **Styling** | Cyberpunk theme, Glassmorphism, 3D effects |
| **Deployment** | Vercel, Environment Variables |
| **Development** | ESLint, TypeScript, Hot Reload |

</div>

## ✨ Key Features

### 🎨 **Design & UX**
- **Cyberpunk Aesthetic**: Futuristic design with neon accents and glassmorphism
- **Responsive Design**: Optimized for desktop, tablet, and mobile devices
- **3D Card Effects**: Enhanced hover animations and depth interactions
- **Smooth Animations**: Framer Motion powered transitions and micro-interactions
- **Loading Experience**: Professional loading screen with progress tracking

### 🤖 **AI-Powered Chatbot**
- **Intelligent Assistant**: Built-in AI chatbot with comprehensive project knowledge
- **Real-time Responses**: Powered by Groq AI for fast, accurate answers
- **Project Context**: Deep knowledge of all projects, technologies, and achievements
- **Conversation Persistence**: Chat history saved across sessions
- **Mobile Optimized**: Responsive chat interface for all devices

### 📱 **Mobile Experience**
- **Instagram-style Navigation**: Bottom navigation bar for mobile users
- **Optimized Layouts**: Tailored mobile layouts for better usability
- **Touch-friendly**: Optimized for touch interactions and gestures

## � Portfolio Sections

| Section | Description |
|---------|-------------|
| **🏠 Hero** | Introduction with key statistics and achievements |
| **💼 Projects** | Detailed showcase of Event Manager and GitIQ |
| **👨‍💻 About** | AI collaboration journey and development methodology |
| **🛠️ Tech Stack** | Comprehensive technology expertise and skills |
| **📞 Contact** | Professional contact information and collaboration opportunities |

## 🚀 Featured Projects

### 🛒 **RakhiMart - E-commerce Platform**
*25,000+ lines | React + TypeScript + Supabase*

<details>
<summary><strong>📊 Project Highlights</strong></summary>

- **🏪 Full E-commerce Platform**: Complete online marketplace for Rakhi sales
- **💳 Payment Integration**: Cashfree with UPI, cards, and net banking support
- **🚚 Multi-Delivery Partners**: Delhivery, Shiprocket, Blue Dart, DTDC integration
- **🤖 AI-Generated Reviews**: Google Generative AI for product reviews
- **📊 Admin Dashboard**: Comprehensive order and inventory management
- **⚡ Real-time Features**: Live order tracking and stock management
- **🔒 Production Security**: Webhook validation and CORS protection
- **📧 Email Integration**: Multi-provider support (SendGrid, Mailgun, SES, Postmark)

**🔗 Links**: [GitHub](https://github.com/DhrubaAgarwalla/RakhiMart) | [Live Demo](https://rakhimart.vercel.app/)

</details>

### 🎯 **NIT Silchar Event Manager**
*75,000+ lines | React + Node.js + Firebase*

<details>
<summary><strong>📊 Project Highlights</strong></summary>

- **🏢 Enterprise-Level Platform**: Comprehensive event management solution
- **📱 QR Code System**: Real-time attendance tracking with email automation
- **📊 Google Sheets Integration**: Automated data pipeline and reporting
- **⚡ Performance**: 70% reduction in event registration time
- **👥 Role-Based Access**: Admin, Club, and Participant management
- **💰 Budget**: Built with $0 investment through AI collaboration
- **⏱️ Timeline**: Completed in 3-4 weeks

**🔗 Links**: [GitHub](https://github.com/DhrubaAgarwalla/NITS-Event-Managment) | [Live Demo](https://nits-event-managment.vercel.app/)

</details>

### 🔍 **GitIQ - AI Repository Insights**
*40,000+ lines | Multi-AI Integration*

<details>
<summary><strong>📊 Project Highlights</strong></summary>

- **🤖 Multi-AI Integration**: Google Gemini, HuggingFace, and custom models
- **⚡ Ultra-Fast Analysis**: 0.12 seconds per commit analysis
- **📈 Health Scoring**: Advanced repository health and quality metrics
- **🔄 Real-time Processing**: Live repository analysis and insights
- **📊 Comprehensive Reports**: Detailed code quality and contribution analysis
- **⏱️ Timeline**: Built in less than a week
- **💰 Budget**: $0 investment through strategic AI orchestration

**🔗 Links**: [GitHub](https://github.com/DhrubaAgarwalla/gitiq) | [Live Demo](https://gitiq.vercel.app/)

</details>

### 🎯 Development Philosophy

This portfolio represents a new paradigm in development - proving that through strategic AI collaboration, prompt engineering, and intelligent resource utilization, one can build enterprise-level applications without traditional coding knowledge.

### 📧 Contact

- **Email**: <EMAIL>
- **GitHub**: [DhrubaAgarwalla](https://github.com/DhrubaAgarwalla)
- **LinkedIn**: [Dhruba Kumar Agarwalla](https://www.linkedin.com/in/dhruba-kumar-agarwalla-7a5346270/)
- **Location**: NIT Silchar, Assam, India

### 🏃‍♂️ Quick Start

```bash
# Clone the repository
git clone <repository-url>

# Navigate to project directory
cd stellar-code-lab

# Install dependencies
npm install

# Start development server
npm run dev
```

### 🌟 Achievements

- 🥈 2nd Place - CSS Hacks Hackathon, NIT Silchar CS Department
- 🚀 115,000+ lines of AI-orchestrated code
- 💰 $0 budget across all projects
- ⚡ Record development speeds through AI collaboration
- 🎓 2nd Year Civil Engineering Student pioneering AI-driven development

---

**"Building the Future, One AI Collaboration at a Time"**

*Created by Dhruba Kumar Agarwalla - AI-Orchestrated Full-Stack Developer*
