{"hash": "8a11b51e", "configHash": "7bbe8bef", "lockfileHash": "042fe382", "browserHash": "69b6c78b", "optimized": {"react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "20d57acb", "needsInterop": true}, "@radix-ui/react-toast": {"src": "../../@radix-ui/react-toast/dist/index.mjs", "file": "@radix-ui_react-toast.js", "fileHash": "3d09736c", "needsInterop": false}, "@radix-ui/react-tooltip": {"src": "../../@radix-ui/react-tooltip/dist/index.mjs", "file": "@radix-ui_react-tooltip.js", "fileHash": "41e16ddf", "needsInterop": false}, "@tanstack/react-query": {"src": "../../@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "2735f3c0", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "f4fba4c3", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "e00f3263", "needsInterop": false}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "6cd82b53", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "8e387718", "needsInterop": false}, "next-themes": {"src": "../../next-themes/dist/index.mjs", "file": "next-themes.js", "fileHash": "68c34a79", "needsInterop": false}, "react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "761b9578", "needsInterop": true}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "71ae11e5", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "a0f6166b", "needsInterop": false}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "e2bae595", "needsInterop": true}, "sonner": {"src": "../../sonner/dist/index.mjs", "file": "sonner.js", "fileHash": "e57544fd", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "fe4a9ea0", "needsInterop": false}}, "chunks": {"chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-CZIX5PKU": {"file": "chunk-CZIX5PKU.js"}, "chunk-R6S4VRB5": {"file": "chunk-R6S4VRB5.js"}, "chunk-S77I6LSE": {"file": "chunk-S77I6LSE.js"}, "chunk-3TFVT2CW": {"file": "chunk-3TFVT2CW.js"}, "chunk-4MBMRILA": {"file": "chunk-4MBMRILA.js"}}}